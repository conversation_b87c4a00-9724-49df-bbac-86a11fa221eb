---
layout: false
---

<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Helper Components Playground</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100vh;
      overflow: hidden;
    }
    #playground-frame {
      width: 100%;
      height: 100vh;
      border: none;
    }
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      color: #666;
      flex-direction: column;
      gap: 16px;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3c78d8;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #e74c3c;
      text-align: center;
      padding: 20px;
    }
    .retry-btn {
      background: #3c78d8;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    .retry-btn:hover {
      background: #2c5aa0;
    }
  </style>
</head>
<body>
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div>正在加载 Helper Components Playground...</div>
    <div style="font-size: 14px; color: #999;">请确保 playground 服务已启动 (http://localhost:3001)</div>
  </div>
  
  <div class="error" id="error" style="display: none;">
    <h3>无法加载 Playground</h3>
    <p>请确保 playground 服务已启动：</p>
    <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; text-align: left; display: inline-block;">
cd playground
pnpm install
pnpm dev</pre>
    <br>
    <button class="retry-btn" onclick="retryLoad()">重试</button>
    <br><br>
    <a href="http://localhost:3001" target="_blank" style="color: #3c78d8;">直接访问 Playground</a>
  </div>
  
  <iframe 
    id="playground-frame" 
    src="http://localhost:3001/" 
    style="display: none;"
    onload="handleLoad()"
    onerror="handleError()">
  </iframe>

  <script>
    let loadTimeout;
    
    function handleLoad() {
      clearTimeout(loadTimeout);
      document.getElementById('loading').style.display = 'none';
      document.getElementById('error').style.display = 'none';
      document.getElementById('playground-frame').style.display = 'block';
    }
    
    function handleError() {
      clearTimeout(loadTimeout);
      document.getElementById('loading').style.display = 'none';
      document.getElementById('error').style.display = 'block';
    }
    
    function retryLoad() {
      document.getElementById('error').style.display = 'none';
      document.getElementById('loading').style.display = 'flex';
      document.getElementById('playground-frame').src = 'http://localhost:3001/?' + Date.now();
    }
    
    // 设置超时，如果5秒内没有加载成功，显示错误信息
    loadTimeout = setTimeout(() => {
      if (document.getElementById('playground-frame').style.display === 'none') {
        handleError();
      }
    }, 5000);
  </script>
</body>
</html>
