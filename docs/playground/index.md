---
layout: page
title: Helper Components Playground
---

# 🎮 Helper Components Playground

在线代码编辑器，支持实时预览和代码分享

## 🚀 快速开始

<div style="margin: 20px 0;">
  <a href="http://localhost:3001" target="_blank" style="display: inline-block; background: #3c78d8; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none; margin-right: 12px;">
    🚀 打开 Playground
  </a>
</div>

## ✨ 功能特性

- 🎯 **实时编辑**: 支持 Vue SFC 语法高亮和智能补全
- 👀 **即时预览**: 代码修改后立即看到效果
- 🔗 **代码分享**: 通过 URL 分享代码片段
- 💾 **项目下载**: 导出为完整的 Vue 项目
- 🎨 **主题切换**: 支持明暗主题切换
- 📱 **响应式设计**: 适配移动端和桌面端

## 📖 使用指南

### 1. 启动 Playground 服务

```bash
cd playground
pnpm install
pnpm dev
```

### 2. 访问 Playground

服务启动后，点击上方的"打开 Playground"按钮，或直接访问 [http://localhost:3001](http://localhost:3001)

### 3. 从文档页面跳转

在组件文档页面中，点击代码示例下方的"在线编辑"按钮，即可跳转到 playground 进行编辑。

## 🛠️ 技术栈

- **Vue 3**: 前端框架
- **@vue/repl**: Vue SFC 在线编辑器核心
- **Monaco Editor**: 代码编辑器（VS Code 同款）
- **Vite**: 构建工具
- **TypeScript**: 类型支持

## 💡 使用技巧

1. **快捷键**: 支持常用的编辑器快捷键，如 `Ctrl+S` 保存等
2. **自动补全**: 输入时会自动提示 Vue API 和组件属性
3. **错误提示**: 实时显示语法错误和类型错误
4. **代码格式化**: 支持代码自动格式化

## 🔧 故障排除

如果 Playground 无法正常加载，请检查：

1. Playground 服务是否已启动 (http://localhost:3001)
2. 端口 3001 是否被其他程序占用
3. 浏览器是否阻止了跨域请求

需要帮助？请查看 [GitHub Issues](https://git.woa.com/wegame/helper/helper-components/issues)
