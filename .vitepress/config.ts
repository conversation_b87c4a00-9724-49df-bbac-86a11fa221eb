import { defineConfig } from 'vitepress'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { demoPlugin } from './plugins/demo-plugin.js'

const __dirname = fileURLToPath(new URL('.', import.meta.url))

export default defineConfig({
  title: 'Helper Components',
  description: 'WeGame Helper Components Library - 组件库文档',
  lang: 'zh-CN',
  
  head: [
    ['meta', { name: 'theme-color', content: '#3c78d8' }],
    ['meta', { name: 'apple-mobile-web-app-capable', content: 'yes' }],
    ['meta', { name: 'apple-mobile-web-app-status-bar-style', content: 'black' }]
  ],

  themeConfig: {
    nav: [
      { text: '首页', link: '/' },
      { text: '快速开始', link: '/docs/guide/getting-started' },
      { text: '组件', link: '/docs/components/' },
      { text: 'Playground', link: '/playground/' },
      { text: 'GitHub', link: 'https://git.woa.com/wegame/helper/helper-components' }
    ],

    sidebar: {
      '/docs/guide/': [
        {
          text: '指南',
          items: [
            { text: '快速开始', link: '/docs/guide/getting-started' },
            { text: '安装使用', link: '/docs/guide/installation' },
            { text: '开发指南', link: '/docs/guide/development' }
          ]
        }
      ],
      '/docs/components/': [
        {
          text: '组件列表',
          items: [
            { text: 'helper-radio', link: '/docs/components/helper-radio/' },
            { text: 'helper-select', link: '/docs/components/helper-select/' },
            { text: 'helper-dialog', link: '/docs/components/helper-dialog/' },
            { text: 'helper-icon-tips', link: '/docs/components/helper-icon-tips/' },
            { text: 'helper-toast', link: '/docs/components/helper-toast/' },
            { text: 'age-tips', link: '/docs/components/age-tips/' },
            { text: 'button-copy', link: '/docs/components/button-copy/' },
            { text: 'loading', link: '/docs/components/loading/' },
            { text: 'helper-image', link: '/docs/components/helper-image/' },
            { text: 'helper-info', link: '/docs/components/helper-info/' },
            { text: 'carousel', link: '/docs/components/carousel/' },
            { text: 'audio-player', link: '/docs/components/audio-player/' },
            { text: 'video-list', link: '/docs/components/video-list/' },
            { text: 'video-player', link: '/docs/components/video-player/' },
            { text: 'live-list', link: '/docs/components/live-list/' },
            { text: 'wallpaper-preview', link: '/docs/components/wallpaper-preview/' },
            { text: 'video-bg', link: '/docs/components/video-bg/' },
            { text: 'video-dialog', link: '/docs/components/video-dialog/' },
            { text: 'feeds-page', link: '/docs/components/feeds-page/' },
            { text: 'gift-popup', link: '/docs/components/gift-popup/' },
            { text: 'helper-kv', link: '/docs/components/helper-kv/' }
          ]
        }
      ]
    },

    socialLinks: [
      { icon: 'github', link: 'https://git.woa.com/wegame/helper/helper-components' }
    ],

    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2024 WeGame Team'
    },

    search: {
      provider: 'local'
    },

    editLink: {
      pattern: 'https://git.woa.com/wegame/helper/helper-components-docs/edit/main/:path',
      text: '在 GitLab 上编辑此页'
    },

    lastUpdated: {
      text: '最后更新',
      formatOptions: {
        dateStyle: 'medium',
        timeStyle: 'short'
      }
    }
  },

  vite: {
    plugins: [
      demoPlugin()
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, '../'),
        'helper-components': resolve(__dirname, '../helper-components')
      }
    },
    server: {
      fs: {
        allow: ['..']
      }
    }
  },

  vue: {
    template: {
      compilerOptions: {
        isCustomElement: (tag) => tag === 'demo' || tag.startsWith('demo-')
      }
    }
  },

  markdown: {
    theme: 'material-theme-palenight',
    lineNumbers: true
  },


})