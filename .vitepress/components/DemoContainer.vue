<template>
  <div class="demo-containers">
    <div class="demo-preview">
      <div class="demo-content">
        <slot name="demo" />
      </div>
    </div>

    <div v-if="showCode" class="demo-code">
      <div class="demo-code-content">
        <slot name="code" />
      </div>
    </div>

    <div v-if="$slots.controls" class="demo-controls">
      <slot name="controls" />
    </div>

    <div class="demo-actions">
      <button
        class="demo-toggle-btn"
        @click="showCode = !showCode"
        :class="{ 'is-active': showCode }"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        <span>{{ showCode ? '隐藏代码' : '查看代码' }}</span>
      </button>

      <button
        v-if="showCode"
        class="demo-playground-btn"
        @click="openInPlayground"
        title="在 Playground 中编辑"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
        </svg>
        <span>在线编辑</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, useSlots } from 'vue'

const showCode = ref(false)
const slots = useSlots()

function openInPlayground() {
  // 尝试从DOM中直接获取代码内容
  const codeBlocks = document.querySelectorAll('.demo-code-content pre code')
  let codeContent = ''

  if (codeBlocks.length > 0) {
    // 获取最后一个代码块的内容（通常是Vue组件代码）
    const lastCodeBlock = codeBlocks[codeBlocks.length - 1]
    codeContent = lastCodeBlock.textContent || lastCodeBlock.innerText || ''
  }

  if (!codeContent.trim()) {
    // 如果没有找到代码，提供一个默认的示例
    codeContent = `<script setup lang="ts">
import { ref } from 'vue'

const msg = ref('Hello Helper Components!')
<\/script>

<template>
  <div class="demo-container">
    <h2>{{ msg }}</h2>
    <p>这里是从文档页面跳转过来的示例</p>
    <p>您可以在这里编写和测试 Helper Components 组件</p>
  </div>
</template>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
}
</style>`
  }

  // 编码代码内容
  const files = {
    'App.vue': codeContent.trim()
  }

  const encodedCode = btoa(encodeURIComponent(JSON.stringify(files)))

  // 构建playground URL - 直接跳转到playground应用
  const playgroundUrl = `http://localhost:3004/#${encodedCode}`

  // 在新窗口中打开playground
  window.open(playgroundUrl, '_blank')
}
</script>

<style scoped>
.demo-containers {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.2s ease;
}

.demo-containers:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.demo-preview {
  background-color: var(--vp-c-bg);
  position: relative;
}

.demo-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  /* background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%); */
}

.demo-code {
  background-color: var(--vp-code-block-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.demo-code-content {
  max-height: 500px;
  overflow-y: auto;
}

.demo-actions {
  padding: 0;
  background: linear-gradient(90deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-mute) 100%);
  border-top: 1px solid var(--vp-c-divider);
  position: relative;
  display: flex;
}

.demo-toggle-btn,
.demo-playground-btn {
  flex: 1;
  padding: 3px 20px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.demo-playground-btn {
  border-left: 1px solid var(--vp-c-divider);
  color: #5580f8;
}

.demo-preview{
  padding: 0 !important;
}

.demo-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-toggle-btn:hover::before {
  left: 100%;
}

.demo-toggle-btn:hover,
.demo-playground-btn:hover {
  color:rgb(94, 130, 192);
}

.demo-toggle-btn.is-active {
  color: rgb(94, 130, 192);
}

.demo-toggle-btn.is-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.demo-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.demo-toggle-btn:hover .demo-icon {
  transform: scale(1.1);
  opacity: 1;
}

.demo-toggle-btn.is-active .demo-icon {
  transform: rotate(180deg);
}

/* 滚动条样式 */
.demo-content::-webkit-scrollbar,
.demo-code-content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.demo-content::-webkit-scrollbar-track,
.demo-code-content::-webkit-scrollbar-track {
  background: transparent;
}

.demo-content::-webkit-scrollbar-thumb,
.demo-code-content::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

.demo-content::-webkit-scrollbar-thumb:hover,
.demo-code-content::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

/* 滚动条交叉角落 */
.demo-content::-webkit-scrollbar-corner,
.demo-code-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.demo-content,
.demo-code-content {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-containers {
    margin: 16px 0;
    border-radius: 8px;
  }

  .demo-content {
    max-height: 300px;
  }

  .demo-code-content {
    max-height: 250px;
  }

  .demo-toggle-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .demo-containers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .demo-containers:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}
</style>