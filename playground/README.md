# Helper Components Playground

这是一个基于 Vue SFC Playground 的在线代码编辑器，专门为 Helper Components 组件库设计。

## 功能特性

- 🎯 **在线编辑**: 支持 Vue 单文件组件的在线编辑
- 🔄 **实时预览**: 代码修改后实时预览效果
- 📤 **代码分享**: 通过 URL 分享代码片段
- 💾 **项目下载**: 将代码导出为完整的 Vue 项目
- 🎨 **主题切换**: 支持明暗主题切换
- 📱 **响应式设计**: 适配移动端和桌面端

## 使用方法

### 启动开发服务器

```bash
cd playground
pnpm install
pnpm dev
```

服务器将在 http://localhost:3001 启动。

### 从文档页面跳转

在组件文档页面中，点击代码示例下方的"在线编辑"按钮，即可跳转到 playground 进行编辑。

### 直接访问

访问 `/playground/` 路径即可直接使用在线编辑器。

## 技术栈

- **Vue 3**: 前端框架
- **@vue/repl**: Vue SFC 在线编辑器核心
- **Monaco Editor**: 代码编辑器
- **Vite**: 构建工具
- **TypeScript**: 类型支持

## 目录结构

```
playground/
├── src/
│   ├── App.vue           # 主应用组件
│   ├── Header.vue        # 头部导航组件
│   ├── store.ts          # 状态管理
│   ├── main.ts           # 入口文件
│   ├── icons/            # 图标组件
│   ├── utils/            # 工具函数
│   └── download/         # 下载功能
├── public/               # 静态资源
├── package.json          # 依赖配置
├── vite.config.ts        # Vite 配置
└── tsconfig.json         # TypeScript 配置
```

## 开发说明

### 添加新的组件支持

1. 修改 `src/store.ts` 中的 `imports` 对象，添加新的组件库导入
2. 更新 `getHelperReplPluginCode` 函数，添加组件初始化逻辑

### 自定义主题

修改 `src/Header.vue` 中的主题配置，添加新的主题选项。

### 扩展功能

可以通过修改 `src/App.vue` 和相关组件来扩展编辑器功能。

## 部署

```bash
pnpm build
```

构建产物将输出到 `dist` 目录。
