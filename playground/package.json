{"name": "helper-components-playground", "version": "1.0.0", "private": true, "description": "Helper Components Playground", "keywords": ["helper-components", "playground", "repl"], "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vue/repl": "^2.4.0", "vue": "^3.3.4"}, "devDependencies": {"@types/node": "^20.5.9", "@vitejs/plugin-vue": "^4.3.4", "typescript": "^5.2.2", "vite": "^4.4.9"}}