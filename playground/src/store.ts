import { reactive, watchEffect } from 'vue'
import { compileFile, File } from '@vue/repl'
import type { OutputModes, SFCOptions, Store, StoreOptions, StoreState } from '@vue/repl'
import * as defaultCompiler from 'vue/compiler-sfc'
import { atou, utoa } from './utils/encode'

const imports = {
  'helper-components': './helper-components.esm.js',
}

const appFile = 'src/App.vue'
const welcomeCode = `\
<script setup lang="ts">
import { ref } from 'vue'

const msg = ref('Hello Helper Components!')
</script>

<template>
  <div class="demo-container">
    <h2>{{ msg }}</h2>
    <p>这里是 Helper Components 在线编辑器</p>
    <p>您可以在这里编写和测试 Helper Components 组件</p>
  </div>
</template>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
}
</style>
`

const importMapFile = 'import-map.json'
const tsconfigFile = 'tsconfig.json'
const tsconfig = {
  compilerOptions: {
    allowJs: true,
    checkJs: true,
    jsx: 'Preserve',
    target: 'ESNext',
    module: 'ESNext',
    moduleResolution: 'Bundler',
    allowImportingTsExtensions: true,
  },
  vueCompilerOptions: {
    target: 3.3,
  },
}



export class ReplStore implements Store {
  state: StoreState

  compiler = defaultCompiler

  options?: SFCOptions

  initialShowOutput: boolean

  initialOutputMode: OutputModes = 'preview'

  customElement = false

  private defaultVueRuntimeURL: string

  private defaultVueServerRendererURL: string

  constructor({
    serializedState = '',
    defaultVueRuntimeURL = `https://cdn.jsdelivr.net/npm/@vue/runtime-dom/dist/runtime-dom.esm-browser.js`,
    defaultVueServerRendererURL = `https://cdn.jsdelivr.net/npm/@vue/server-renderer/dist/server-renderer.esm-browser.js`,
    showOutput = false,
    outputMode = 'preview',
  }: StoreOptions = {}) {
    const files: StoreState['files'] = {}

    if (serializedState) {
      const saved = JSON.parse(atou(serializedState))

      for (const filename in saved) {
        setFile(files, filename, saved[filename])
      }
    } else {
      setFile(files, appFile, welcomeCode)
    }

    this.defaultVueRuntimeURL = defaultVueRuntimeURL
    this.defaultVueServerRendererURL = defaultVueServerRendererURL
    this.initialShowOutput = showOutput
    this.initialOutputMode = outputMode as OutputModes

    this.state = reactive({
      files,
      mainFile: appFile,
      activeFile: files[appFile],
      errors: [],
      vueRuntimeURL: this.defaultVueRuntimeURL,
      vueServerRendererURL: this.defaultVueServerRendererURL,
      typescriptVersion: 'latest',
      resetFlip: true,
    })

    this.initImportMap()
    this.initTsConfig()
  }

  init() {
    watchEffect(() => compileFile(this, this.state.activeFile).then((errs) => (this.state.errors = errs)))

    this.state.errors = []

    for (const file in this.state.files) {
      if (file !== appFile) {
        compileFile(this, this.state.files[file]).then((errs) => this.state.errors.push(...errs))
      }
    }
  }

  private initTsConfig() {
    if (!this.state.files[tsconfigFile]) {
      this.setTsConfig(tsconfig)
    }
  }

  setTsConfig(config: any) {
    this.state.files[tsconfigFile] = new File(tsconfigFile, JSON.stringify(config, undefined, 2))
  }

  getTsConfig() {
    try {
      return JSON.parse(this.state.files[tsconfigFile].code)
    } catch {
      return {}
    }
  }

  setActive(filename: string) {
    this.state.activeFile = this.state.files[filename]
  }

  addFile(fileOrFilename: string | File): void {
    const file = typeof fileOrFilename === 'string' ? new File(fileOrFilename) : fileOrFilename
    this.state.files[file.filename] = file
    if (!file.hidden) {
      this.setActive(file.filename)
    }
  }

  deleteFile(filename: string) {
    if (filename === appFile) {
      console.warn('Cannot delete the main App.vue file')
      return
    }

    if (confirm(`Are you sure you want to delete ${stripSrcPrefix(filename)}?`)) {
      if (this.state.activeFile.filename === filename) {
        this.state.activeFile = this.state.files[this.state.mainFile]
      }
      delete this.state.files[filename]
    }
  }

  renameFile(oldFilename: string, newFilename: string) {
    const { files } = this.state
    const file = files[oldFilename]

    if (!file) {
      this.state.errors = [`Could not rename "${oldFilename}", file not found`]
      return
    }

    if (!newFilename || oldFilename === newFilename) {
      this.state.errors = [`Cannot rename "${oldFilename}" to "${newFilename}"`]
      return
    }

    file.filename = newFilename

    const newFiles: Record<string, File> = {}

    // Preserve iteration order for files
    for (const name in files) {
      if (name === oldFilename) {
        newFiles[newFilename] = file
      } else {
        newFiles[name] = files[name]
      }
    }

    this.state.files = newFiles

    if (this.state.mainFile === oldFilename) {
      this.state.mainFile = newFilename
    }

    compileFile(this, file).then((errs) => (this.state.errors = errs))
  }

  serialize() {
    const files = this.getFiles()
    const importMap = files[importMapFile]
    if (importMap) {
      const { imports } = JSON.parse(importMap)
      if (imports.vue === this.defaultVueRuntimeURL) {
        delete imports.vue
      }
      if (imports['vue/server-renderer'] === this.defaultVueServerRendererURL) {
        delete imports['vue/server-renderer']
      }
      if (!Object.keys(imports).length) {
        delete files[importMapFile]
      } else {
        files[importMapFile] = JSON.stringify({ imports }, null, 2)
      }
    }
    return '#' + utoa(JSON.stringify(files))
  }

  getFiles() {
    const exported: Record<string, string> = {}

    for (const filename in this.state.files) {
      const normalized = filename === importMapFile ? filename : filename.replace(/^src\//, '')
      exported[normalized] = this.state.files[filename].code
    }

    return exported
  }



  private initImportMap() {
    const map = this.state.files['import-map.json']
    if (!map) {
      this.state.files['import-map.json'] = new File(
        'import-map.json',
        JSON.stringify(
          {
            imports: {
              vue: this.defaultVueRuntimeURL,
              ...imports,
              'vue/server-renderer': this.defaultVueServerRendererURL,
            },
          },
          null,
          2,
        ),
      )
    } else {
      try {
        const json = JSON.parse(map.code)
        if (!json.imports.vue) {
          json.imports.vue = this.defaultVueRuntimeURL
        } else {
          json.imports.vue = fixURL(json.imports.vue)
        }
        if (!json.imports['vue/server-renderer']) {
          json.imports['vue/server-renderer'] = this.defaultVueServerRendererURL
        } else {
          json.imports['vue/server-renderer'] = fixURL(json.imports['vue/server-renderer'])
        }
        map.code = JSON.stringify(json, null, 2)
      } catch {}
    }
  }

  getImportMap() {
    try {
      return JSON.parse(this.state.files[importMapFile].code)
    } catch (e) {
      this.state.errors = [`Syntax error in import-map.json: ${(e as Error).message}`]
      return {}
    }
  }

  setImportMap(map: { imports: Record<string, string>; scopes?: Record<string, Record<string, string>> }) {
    this.state.files[importMapFile]!.code = JSON.stringify(map, null, 2)
  }


}

function setFile(files: Record<string, File>, filename: string, content: string, hidden = false) {
  // prefix user files with src/
  // for cleaner Volar path completion when using Monaco editor
  const normalized =
    filename !== importMapFile && filename !== tsconfigFile && !filename.startsWith('src/')
      ? `src/${filename}`
      : filename
  files[normalized] = new File(normalized, content, hidden)
}

function fixURL(url: string) {
  return url.replace('https://sfc.vuejs', 'https://play.vuejs')
}

function stripSrcPrefix(file: string) {
  return file.replace(/^src\//, '')
}
