<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'
import Close from './icons/Close.vue'
import GitHub from './icons/GitHub.vue'
import Share from './icons/Share.vue'

const props = defineProps(['store', 'theme'])
const emit = defineEmits(['update:theme'])
const inIframe = ref(window.self !== window.top)
const currentVueVersion = ref('')
const vueVersions = ref<string[]>([])
const currentTheme = ref(props.theme)

onMounted(() => {
  const initialTheme = getInitialTheme()
  if (initialTheme) {
    emit('update:theme', initialTheme)
    currentTheme.value = initialTheme
  }

  fetchVueVersions()
  nextTick().then(syncTheme)
})

watch(
  () => props.theme,
  () => {
    currentTheme.value = props.theme
    syncTheme()
  },
)
watch(
  () => currentTheme.value,
  () => {
    emit('update:theme', currentTheme.value)
  },
)
watch(() => currentVueVersion.value, setVueVersion)

async function copyLink() {
  await navigator.clipboard.writeText(location.href)
  alert('分享链接已复制到剪贴板')
}

function openGithub() {
  window.open('https://git.woa.com/wegame/helper/helper-components', '_blank')
}

function handleClose() {
  window.parent.postMessage({ action: 'playground-close' }, '*')
}

function getInitialTheme() {
  const search = new URLSearchParams(window.location.search)
  return search.get('initialTheme')
}

function syncTheme() {
  localStorage.setItem('helper-components-playground-theme', props.theme)
  
  props.theme.toLowerCase().includes('dark')
    ? document.documentElement.classList.add('dark')
    : document.documentElement.classList.remove('dark')
  document.documentElement.setAttribute('theme', props.theme)
}

async function fetchVueVersions() {
  try {
    const res = await fetch('https://api.github.com/repos/vuejs/core/releases?per_page=100')
    const releases: any[] = await res.json()
    const versions = releases.map((r) => (/^v/.test(r.tag_name) ? r.tag_name.slice(1) : r.tag_name))
    vueVersions.value = versions
  } catch (error) {
    console.error('Failed to fetch Vue versions:', error)
  }
}

async function setVueVersion(v: string) {
  await props.store.setVueVersion(v)
}

function toggleTheme() {
  const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
  currentTheme.value = newTheme
}
</script>

<template>
  <nav>
    <Close v-if="inIframe" class="close" @click="handleClose" />
    <h1 v-else>
      <span>Helper Components Playground</span>
    </h1>
    <div class="nav-actions">
      <select
        v-model="currentVueVersion"
        class="version-select"
        title="Vue Version"
      >
        <option value="">Vue Version</option>
        <option v-for="v in vueVersions" :key="v" :value="v">v{{ v }}</option>
      </select>

      <button class="action-button" @click="toggleTheme" title="切换主题">
        {{ currentTheme === 'light' ? '🌙' : '☀️' }}
      </button>

      <button class="action-button" @click="copyLink" title="复制链接">
        <Share />
      </button>

      <button class="action-button" @click="openGithub" title="打开 GitHub">
        <GitHub />
      </button>
    </div>
  </nav>
</template>

<style scoped>
nav {
  --bg: #fff;
  --bg-light: #fff;
  --border: #ddd;
  --text: #333;

  color: var(--text);
  height: var(--nav-height);
  box-sizing: border-box;
  padding: 0 1em;
  background-color: var(--bg);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark nav {
  --bg: #1a1a1a;
  --bg-light: #242424;
  --border: #383838;
  --text: #ddd;

  box-shadow: none;
  border-bottom: 1px solid var(--border);
}

h1 {
  margin: 0;
  line-height: var(--nav-height);
  font-weight: 500;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.version-select {
  padding: 6px 12px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--bg);
  color: var(--text);
  font-size: 14px;
  min-width: 120px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg);
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-light);
  transform: translateY(-1px);
}

.close {
  cursor: pointer;
  width: 24px;
  height: 24px;
}

@media (max-width: 768px) {
  h1 span {
    display: none;
  }
  
  .version-select {
    display: none;
  }
  
  .nav-actions {
    gap: 8px;
  }
  
  .action-button {
    width: 32px;
    height: 32px;
  }
}
</style>
