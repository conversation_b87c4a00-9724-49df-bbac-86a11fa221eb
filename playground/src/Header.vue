<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'
import Close from './icons/Close.vue'
import GitHub from './icons/GitHub.vue'
import Share from './icons/Share.vue'

const props = defineProps(['store', 'theme'])
const emit = defineEmits(['update:theme'])
const inIframe = ref(window.self !== window.top)
const currentTheme = ref(props.theme)

onMounted(() => {
  const initialTheme = getInitialTheme()
  if (initialTheme) {
    emit('update:theme', initialTheme)
    currentTheme.value = initialTheme
  }

  nextTick().then(syncTheme)
})

watch(
  () => props.theme,
  () => {
    currentTheme.value = props.theme
    syncTheme()
  },
)
watch(
  () => currentTheme.value,
  () => {
    emit('update:theme', currentTheme.value)
  },
)

async function copyLink() {
  await navigator.clipboard.writeText(location.href)
  alert('分享链接已复制到剪贴板')
}

function openGithub() {
  window.open('https://git.woa.com/wegame/helper/helper-components', '_blank')
}

function handleClose() {
  window.parent.postMessage({ action: 'playground-close' }, '*')
}

function getInitialTheme() {
  const search = new URLSearchParams(window.location.search)
  return search.get('initialTheme')
}

function syncTheme() {
  localStorage.setItem('helper-components-playground-theme', props.theme)
  
  props.theme.toLowerCase().includes('dark')
    ? document.documentElement.classList.add('dark')
    : document.documentElement.classList.remove('dark')
  document.documentElement.setAttribute('theme', props.theme)
}



function toggleTheme() {
  const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
  currentTheme.value = newTheme
}
</script>

<template>
  <nav>
    <Close v-if="inIframe" class="close" @click="handleClose" />
    <h1 v-else>
      <span>Helper Components Playground</span>
    </h1>
    <div class="nav-actions">
      <button class="action-button" @click="toggleTheme" title="切换主题">
        <svg v-if="currentTheme === 'light'" viewBox="0 0 24 24" width="18" height="18">
          <path fill="currentColor" d="M17.75,4.09L15.22,6.03L16.13,9.09L13.5,7.28L10.87,9.09L11.78,6.03L9.25,4.09L12.44,4L13.5,1L14.56,4L17.75,4.09M21.25,11L19.61,12.25L20.2,14.23L18.5,13.06L16.8,14.23L17.39,12.25L15.75,11L17.81,10.95L18.5,9L19.19,10.95L21.25,11M18.97,15.95C19.8,15.87 20.69,17.05 20.16,17.8C19.84,18.25 19.5,18.67 19.08,19.07C15.17,23 8.84,23 4.94,19.07C1.03,15.17 1.03,8.83 4.94,4.93C5.34,4.53 5.76,4.17 6.21,3.85C6.96,3.32 8.14,4.21 8.06,5.04C7.79,7.9 8.75,10.87 10.95,13.06C13.14,15.26 16.1,16.22 18.97,15.95M17.33,17.97C14.5,17.81 11.7,16.64 9.53,14.5C7.36,12.31 6.2,9.5 6.04,6.68C3.23,9.82 3.34,14.4 6.35,17.41C9.37,20.43 14,20.54 17.33,17.97Z"/>
        </svg>
        <svg v-else viewBox="0 0 24 24" width="18" height="18">
          <path fill="currentColor" d="M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z"/>
        </svg>
      </button>

      <button class="action-button" @click="copyLink" title="复制链接">
        <Share />
      </button>

      <button class="action-button" @click="openGithub" title="打开 GitHub">
        <GitHub />
      </button>
    </div>
  </nav>
</template>

<style scoped>
nav {
  --bg: #fff;
  --bg-light: #fff;
  --border: #ddd;
  --text: #333;

  color: var(--text);
  height: var(--nav-height);
  box-sizing: border-box;
  padding: 0 1em;
  background-color: var(--bg);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark nav {
  --bg: #1a1a1a;
  --bg-light: #242424;
  --border: #383838;
  --text: #ddd;

  box-shadow: none;
  border-bottom: 1px solid var(--border);
}

h1 {
  margin: 0;
  line-height: var(--nav-height);
  font-weight: 500;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--border);
  border-radius: 50%;
  background: var(--bg);
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: var(--bg-light);
  transform: translateY(-1px);
}

.close {
  cursor: pointer;
  width: 24px;
  height: 24px;
}

@media (max-width: 768px) {
  h1 span {
    display: none;
  }

  .nav-actions {
    gap: 8px;
  }

  .action-button {
    width: 32px;
    height: 32px;
  }
}
</style>
