import { saveAs } from 'file-saver'
import type { ReplStore } from '../store'

export async function downloadProject(store: ReplStore) {
  const { default: JSZip } = await import('jszip')
  const zip = new JSZip()

  // 添加文件到 zip
  const files = store.getFiles()
  
  // 创建基本的项目结构
  zip.file('package.json', JSON.stringify({
    name: 'helper-components-playground-project',
    version: '1.0.0',
    type: 'module',
    scripts: {
      dev: 'vite',
      build: 'vite build',
      preview: 'vite preview'
    },
    dependencies: {
      vue: '^3.3.4'
    },
    devDependencies: {
      '@vitejs/plugin-vue': '^4.3.4',
      vite: '^4.4.9'
    }
  }, null, 2))

  // 添加 vite 配置
  zip.file('vite.config.js', `import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
})`)

  // 添加 index.html
  zip.file('index.html', `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Helper Components Project</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>`)

  // 添加 main.js
  zip.file('src/main.js', `import { createApp } from 'vue'
import App from './App.vue'

createApp(App).mount('#app')`)

  // 添加用户的文件
  for (const file in files) {
    if (file !== 'import-map.json') {
      zip.file(file, files[file])
    }
  }

  const blob = await zip.generateAsync({ type: 'blob' })
  saveAs(blob, 'helper-components-project.zip')
}
